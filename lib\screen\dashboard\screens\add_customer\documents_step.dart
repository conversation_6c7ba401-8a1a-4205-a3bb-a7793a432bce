import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/widgets/customer/customer_section_title.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer_documents_section.dart';
import 'package:lc_work_flow/models/collateral_type.dart';

class DocumentsStep extends StatelessWidget {
  final File? idCardImage;
  final Map<CollateralType, File?> collateralImages;
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, File?) onImagePicked;

  const DocumentsStep({
    Key? key,
    required this.idCardImage,
    required this.collateralImages,
    required this.selectedCollateralTypes,
    required this.onImagePicked,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomerSectionTitle(title: 'Documents', icon: Icons.insert_drive_file),
            const SizedBox(height: 16),
            CustomerDocumentsSection(
              idCardImage: idCardImage,
              collateralImages: collateralImages,
              selectedCollateralTypes: selectedCollateralTypes,
              onImagePicked: onImagePicked,
            ),
          ],
        ),
      ),
    );
  }
}
