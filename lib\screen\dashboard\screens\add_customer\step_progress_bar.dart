import 'package:flutter/material.dart';


class StepProgressBar extends StatelessWidget {
  final int currentStep;
  final List<String> steps;

  const StepProgressBar({
    Key? key,
    required this.currentStep,
    required this.steps,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    const activeColor = Color(0xFF12306E);
    const inactiveColor = Colors.grey;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
      child: Row(
        children: List.generate(steps.length, (index) {
          final isCompleted = index < currentStep;
          final isActive = index == currentStep;

          return Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Divider(
                        color: isCompleted || isActive ? activeColor : inactiveColor,
                        thickness: 2,
                        height: 2,
                      ),
                    ),
                    if (index == 0) const Spacer(),
                    if (index > 0) 
                      Expanded(
                        child: Divider(
                          color: isCompleted ? activeColor : inactiveColor,
                          thickness: 2,
                          height: 2,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  steps[index],
                  style: TextStyle(
                    color: isActive || isCompleted ? activeColor : inactiveColor,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
