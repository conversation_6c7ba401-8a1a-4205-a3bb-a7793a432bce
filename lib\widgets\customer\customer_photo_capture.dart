import 'dart:io';
import 'package:flutter/material.dart';

import 'package:image_picker/image_picker.dart';

class CustomerPhotoCapture extends StatelessWidget {
  final String title;
  final File? imageFile;
  final Function(File) onImagePicked;
  final bool isSelfie;
  const CustomerPhotoCapture({
    super.key,
    required this.title,
    required this.imageFile,
    required this.onImagePicked,
    required this.isSelfie,
  });

  Future<void> _pickImage(BuildContext context) async {
    final picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.camera,
      preferredCameraDevice: isSelfie ? CameraDevice.front : CameraDevice.rear,
      imageQuality: 90,
    );
    if (image != null) {
      onImagePicked(File(image.path));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _pickImage(context),
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            width: double.infinity,
            height: 180,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: imageFile != null
                  ? Image.file(imageFile!, fit: BoxFit.cover, width: double.infinity)
                  : const Center(child: Icon(Icons.camera_alt, size: 48, color: Colors.grey)),
            ),
          ),
        ),
      ],
    );
  }
}
