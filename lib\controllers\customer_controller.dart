import 'package:get/get.dart';
import '../models/customer_model.dart';

class CustomerController extends GetxController {
  // Use .obs to make the list reactive
  var allCustomers = <Customer>[].obs;

  @override
  void onInit() {
    super.onInit();
    // Load initial mock data
    _loadMockCustomers();
  }

  void _loadMockCustomers() {
    allCustomers.assignAll([
      Customer(
        id: '1',
        name: '<PERSON><PERSON>',
        phone: '+855 12 345 678',
        nid: 'NID001',
        profileImage: 'https://i.pravatar.cc/150?img=1',
        loanAmount: 2500,
        interestRate: 1.5,
        loanStatus: LoanStatus.disbursed,
        loanPurpose: 'Small Business',
        loanStartDate: DateTime.now().subtract(const Duration(days: 15)),
        loanEndDate: DateTime.now().add(const Duration(days: 15)),
      ),
      Customer(
        id: '2',
        name: '<PERSON><PERSON>',
        phone: '+855 23 456 789',
        nid: 'NID002',
        profileImage: 'https://i.pravatar.cc/150?img=2',
        loanAmount: 5000,
        interestRate: 1.8,
        loanStatus: LoanStatus.approved,
        loanPurpose: 'Education',
        loanStartDate: DateTime.now().add(const Duration(days: 2)),
        loanEndDate: DateTime.now().add(const Duration(days: 32)),
      ),
      Customer(
        id: '3',
        name: 'Sopheap Lim',
        phone: '+855 34 567 890',
        nid: 'NID003',
        profileImage: 'https://i.pravatar.cc/150?img=3',
        loanAmount: 1000,
        interestRate: 2.0,
        loanStatus: LoanStatus.pending,
        loanPurpose: 'Emergency',
        loanStartDate: DateTime.now(),
        loanEndDate: DateTime.now().add(const Duration(days: 30)),
      ),
      Customer(
        id: '4',
        name: 'Rithy Chhun',
        phone: '+855 45 678 901',
        nid: 'NID004',
        profileImage: 'https://i.pravatar.cc/150?img=4',
        loanAmount: 7500,
        interestRate: 1.2,
        loanStatus: LoanStatus.completed,
        loanPurpose: 'Agriculture',
        loanStartDate: DateTime.now().subtract(const Duration(days: 60)),
        loanEndDate: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Customer(
        id: '5',
        name: 'Sreyneang Oum',
        phone: '+855 56 789 012',
        nid: 'NID005',
        profileImage: 'https://i.pravatar.cc/150?img=5',
        loanAmount: 3000,
        interestRate: 2.5,
        loanStatus: LoanStatus.rejected,
        loanPurpose: 'Home Renovation',
        loanStartDate: DateTime.now().subtract(const Duration(days: 5)),
        loanEndDate: DateTime.now().add(const Duration(days: 25)),
      ),
    ]);
  }

  void addCustomer(Customer customer) {
    allCustomers.add(customer);
  }
}
