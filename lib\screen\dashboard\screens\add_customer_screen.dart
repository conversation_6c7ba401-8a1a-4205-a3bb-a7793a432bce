import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart';

import 'package:lc_work_flow/screen/dashboard/screens/add_customer/borrower_info_step.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/loan_info_step.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/documents_step.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/review_step.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
import 'package:lc_work_flow/models/product_type.dart';
import 'package:lc_work_flow/models/document_type.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/step_progress_bar.dart';
import 'package:lc_work_flow/services/khmer_text_recognizer.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  int _currentStep = 0;
  final List<GlobalKey<FormState>> _formKeys = List.generate(
    4,
    (_) => GlobalKey<FormState>(),
  );

  // Controllers for personal info
  final TextEditingController _fullNameKhmerController =
      TextEditingController();
  final TextEditingController _fullNameLatinController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _idNumberController = TextEditingController();

  // Borrower info step required state
  DocumentType? _selectedDocumentType;

  // Loan info controllers
  final TextEditingController _requestedAmountController =
      TextEditingController();
  final TextEditingController _loanTermController = TextEditingController();
  DateTime? _dateOfBirth;
  DateTime? _disbursementDate;
  ProductType? _selectedProductType;
  final Set<CollateralType> _selectedCollateralTypes = <CollateralType>{};
  final Map<CollateralType, File?> _collateralImages =
      <CollateralType, File?>{};
  // Guarantor section controllers
  final TextEditingController _guarantorNameController =
      TextEditingController();
  final TextEditingController _guarantorPhoneController =
      TextEditingController();
  bool get _showGuarantorSection =>
      _selectedCollateralTypes.contains(CollateralType.guarantor) ||
      _selectedCollateralTypes.contains(CollateralType.coBorrower);

  File? _idCardImage;

  @override
  void dispose() {
    _fullNameKhmerController.dispose();
    _fullNameLatinController.dispose();
    _phoneController.dispose();
    _idNumberController.dispose();
    _requestedAmountController.dispose();
    _loanTermController.dispose();
    super.dispose();
  }

  void _goToNextStep() {
    if (_formKeys[_currentStep].currentState?.validate() ?? false) {
      if (_currentStep < 3) {
        setState(() => _currentStep++);
      } else {
        _submitForm();
      }
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
    }
  }

  void _submitForm() {
    // TODO: Implement your submission logic here
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Customer submitted!')));
  }

  Future<void> _scanDocument() async {
    try {
      // Show loading dialog
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // Pick image from camera
      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 90,
      );

      if (image != null) {
        final imageFile = File(image.path);

        // Store the captured image
        setState(() {
          _idCardImage = imageFile;
        });

        // Perform OCR text recognition
        final recognizedText = await KhmerTextRecognizer.recognizeText(
          imageFile,
        );

        // Close loading dialog
        Get.back();

        if (recognizedText.isNotEmpty && !recognizedText.startsWith('Error:')) {
          // Extract structured information from recognized text
          final extractedInfo = KhmerTextRecognizer.extractInformation(
            recognizedText,
          );

          // Populate fields with extracted information
          _populateFieldsFromExtractedInfo(extractedInfo);

          // Show success message with recognized text and extracted info
          Get.dialog(
            AlertDialog(
              title: const Text('Document Scanned Successfully'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Recognized text:'),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        recognizedText,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    if (extractedInfo.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text('Extracted information:'),
                      const SizedBox(height: 8),
                      ...extractedInfo.entries.map(
                        (entry) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            '${entry.key}: ${entry.value}',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    const Text(
                      'Please verify and correct the auto-filled information.',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        } else {
          // Show error message
          Get.snackbar(
            'OCR Error',
            'Could not extract text from the document. Please enter information manually.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
          );
        }
      } else {
        // Close loading dialog if no image was selected
        Get.back();
      }
    } catch (e) {
      // Close loading dialog and show error
      Get.back();
      Get.snackbar(
        'Error',
        'Failed to scan document: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _populateFieldsFromExtractedInfo(Map<String, String> extractedInfo) {
    // Populate form fields with extracted information, only if fields are currently empty
    if (extractedInfo.containsKey('phone') && _phoneController.text.isEmpty) {
      _phoneController.text = extractedInfo['phone']!;
    }

    if (extractedInfo.containsKey('idNumber') &&
        _idNumberController.text.isEmpty) {
      _idNumberController.text = extractedInfo['idNumber']!;
    }

    if (extractedInfo.containsKey('khmerName') &&
        _fullNameKhmerController.text.isEmpty) {
      _fullNameKhmerController.text = extractedInfo['khmerName']!;
    }

    if (extractedInfo.containsKey('latinName') &&
        _fullNameLatinController.text.isEmpty) {
      _fullNameLatinController.text = extractedInfo['latinName']!;
    }

    // Handle date extraction for date of birth
    if (extractedInfo.containsKey('date') && _dateOfBirth == null) {
      try {
        final dateString = extractedInfo['date']!;
        // Try to parse common date formats
        DateTime? parsedDate;

        // Try DD/MM/YYYY format
        if (dateString.contains('/')) {
          final parts = dateString.split('/');
          if (parts.length == 3) {
            final day = int.tryParse(parts[0]);
            final month = int.tryParse(parts[1]);
            final year = int.tryParse(parts[2]);
            if (day != null && month != null && year != null) {
              // Handle 2-digit years
              final fullYear =
                  year < 100 ? (year > 50 ? 1900 + year : 2000 + year) : year;
              parsedDate = DateTime(fullYear, month, day);
            }
          }
        }

        // Try DD-MM-YYYY format
        if (parsedDate == null && dateString.contains('-')) {
          final parts = dateString.split('-');
          if (parts.length == 3) {
            final day = int.tryParse(parts[0]);
            final month = int.tryParse(parts[1]);
            final year = int.tryParse(parts[2]);
            if (day != null && month != null && year != null) {
              final fullYear =
                  year < 100 ? (year > 50 ? 1900 + year : 2000 + year) : year;
              parsedDate = DateTime(fullYear, month, day);
            }
          }
        }

        if (parsedDate != null && parsedDate.isBefore(DateTime.now())) {
          setState(() {
            _dateOfBirth = parsedDate;
          });
        }
      } catch (e) {
        // If date parsing fails, ignore and continue
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(
          'ពាក្យស្នើ​សុំខ្ចីប្រាក់ និងដាក់បញ្ចាំ',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(color: Colors.white),
        ),
        centerTitle: true,
        backgroundColor: const Color(0xFF12306E),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child: Column(
          children: [
            StepProgressBar(
              currentStep: _currentStep,
              steps: const [
                'ព័ត៌មានអតិថិជន',
                'ព័ត៌មានកខ្ចីប្រាក់',
                'ឯកសារយោង',
                'ផ្ទៀងផ្ទាត់',
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 4.0,
                ),
                child: _buildStepContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepContent() {
    final isLastStep = _currentStep == 3;
    Widget content;
    switch (_currentStep) {
      case 0:
        content = Form(
          key: _formKeys[0],
          child: BorrowerInfoStep(
            fullNameKhmerController: _fullNameKhmerController,
            fullNameLatinController: _fullNameLatinController,
            phoneController: _phoneController,
            idNumberController: _idNumberController,
            selectedDocumentType: _selectedDocumentType,
            onDocumentTypeChanged:
                (docType) => setState(() => _selectedDocumentType = docType),
            dateOfBirth: _dateOfBirth,
            onDateOfBirthChanged: (date) => setState(() => _dateOfBirth = date),
            onScanDocument: _scanDocument,
          ),
        );
        break;
      case 1:
        content = Form(
          key: _formKeys[1],
          child: LoanInfoStep(
            requestedAmountController: _requestedAmountController,
            selectedProductType: _selectedProductType,
            onProductTypeChanged:
                (type) => setState(() => _selectedProductType = type),
            loanTermController: _loanTermController,
            disbursementDate: _disbursementDate,
            onDisbursementDateChanged:
                (date) => setState(() => _disbursementDate = date),
            selectedCollateralTypes: _selectedCollateralTypes,
            collateralImages: _collateralImages,
            onCollateralTypeChanged:
                (type, selected) => setState(() {
                  if (selected) {
                    _selectedCollateralTypes.add(type);
                  } else {
                    _selectedCollateralTypes.remove(type);
                  }
                }),
            onImagePicked:
                (type, photo) =>
                    setState(() => _collateralImages[type] = photo),
            guarantorNameController: _guarantorNameController,
            guarantorPhoneController: _guarantorPhoneController,
            showGuarantorSection: _showGuarantorSection,
          ),
        );
        break;
      case 2:
        content = Form(
          key: _formKeys[2],
          child: DocumentsStep(
            idCardImage: _idCardImage,
            collateralImages: _collateralImages,
            selectedCollateralTypes: _selectedCollateralTypes,
            onImagePicked:
                (type, photo) =>
                    setState(() => _collateralImages[type] = photo),
          ),
        );
        break;
      case 3:
      default:
        content = Form(
          key: _formKeys[3],
          child: ReviewStep(
            fullNameKhmer: _fullNameKhmerController.text,
            phoneNumber: _phoneController.text,
            fullNameLatin: _fullNameLatinController.text,
            idNumber: _idNumberController.text,
            dateOfBirth: _dateOfBirth,
            requestedAmount: _requestedAmountController.text,
            productTypeDisplayName: _selectedProductType?.displayName ?? '',
            loanTerm: _loanTermController.text,
            disbursementDate: _disbursementDate,
            idCardUploaded: _idCardImage != null,
            borrowerNidPhotoUploaded:
                _collateralImages[CollateralType.values.byName('nid')] != null,
            borrowerHomePhotoUploaded:
                _collateralImages[CollateralType.values.byName('homePhoto')] !=
                null,
            borrowerBusinessPhotoUploaded:
                _collateralImages[CollateralType.values.byName(
                  'businessPhoto',
                )] !=
                null,
          ),
        );
        break;
    }
    return Column(
      children: [
        content,
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center, // Center buttons
          children: [
            if (_currentStep > 0)
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: OutlinedButton(
                  onPressed: _goToPreviousStep,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Back'),
                ),
              ),
            ElevatedButton(
              onPressed: _goToNextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF12306E),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(isLastStep ? 'Submit' : 'Continue'),
            ),
          ],
        ),
      ],
    );
  }
}
