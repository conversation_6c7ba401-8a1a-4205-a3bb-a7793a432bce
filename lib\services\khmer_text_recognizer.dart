import 'dart:io';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class KhmerTextRecognizer {
  /// Recognizes text from an image file, attempting both Latin and Khmer scripts
  static Future<String> recognizeText(File imageFile) async {
    final inputImage = InputImage.fromFile(imageFile);

    try {
      // First try with Latin script (more reliable for mixed content)
      final latinRecognizer = TextRecognizer(
        script: TextRecognitionScript.latin,
      );
      final latinResult = await latinRecognizer.processImage(inputImage);
      await latinRecognizer.close();

      String combinedText = latinResult.text;

      // If we have some text but it might contain Khmer characters,
      // also try with Chinese script which sometimes works better for Khmer
      if (combinedText.isNotEmpty) {
        try {
          final chineseRecognizer = TextRecognizer(
            script: TextRecognitionScript.chinese,
          );
          final chineseResult = await chineseRecognizer.processImage(
            inputImage,
          );
          await chineseRecognizer.close();

          // Combine results, preferring Latin for Latin characters and Chinese for others
          if (chineseResult.text.isNotEmpty &&
              chineseResult.text != combinedText) {
            combinedText = '$combinedText\n${chineseResult.text}';
          }
        } catch (e) {
          // If Chinese recognition fails, continue with Latin only
          // Note: In production, consider using a proper logging framework
        }
      }

      return _cleanupRecognizedText(combinedText);
    } catch (e) {
      return 'Error: $e';
    }
  }

  /// Cleans up and processes the recognized text
  static String _cleanupRecognizedText(String text) {
    if (text.isEmpty) return text;

    // Remove excessive whitespace and normalize line breaks
    text = text.replaceAll(RegExp(r'\s+'), ' ');
    text = text.replaceAll(RegExp(r'\n\s*\n'), '\n');

    // Split into lines and clean each line
    final lines =
        text
            .split('\n')
            .map((line) => line.trim())
            .where((line) => line.isNotEmpty)
            .toList();

    // Remove duplicate lines
    final uniqueLines = <String>[];
    for (String line in lines) {
      if (!uniqueLines.any((existing) => _areSimilarLines(existing, line))) {
        uniqueLines.add(line);
      }
    }

    return uniqueLines.join('\n');
  }

  /// Checks if two lines are similar (to remove duplicates from multiple OCR attempts)
  static bool _areSimilarLines(String line1, String line2) {
    if (line1 == line2) return true;

    // Remove spaces and compare
    final clean1 = line1.replaceAll(RegExp(r'\s+'), '');
    final clean2 = line2.replaceAll(RegExp(r'\s+'), '');

    if (clean1 == clean2) return true;

    // Check if one is a substring of the other (with some tolerance)
    if (clean1.length > 3 && clean2.length > 3) {
      return clean1.contains(clean2) || clean2.contains(clean1);
    }

    return false;
  }

  /// Extracts specific information types from recognized text
  static Map<String, String> extractInformation(String recognizedText) {
    final result = <String, String>{};
    final lines =
        recognizedText
            .split('\n')
            .map((line) => line.trim())
            .where((line) => line.isNotEmpty)
            .toList();

    for (String line in lines) {
      // Skip lines that are too short or contain only common words
      if (line.length < 4) continue;

      // Extract phone numbers (Cambodian formats)
      final phoneRegex = RegExp(r'(\+855|855|0)?\s*[1-9]\d{7,8}');
      final phoneMatch = phoneRegex.firstMatch(line);
      if (phoneMatch != null && !result.containsKey('phone')) {
        String phone = phoneMatch.group(0) ?? '';
        // Only keep if it's a valid length
        if (phone.replaceAll(RegExp(r'[^\d]'), '').length >= 8) {
          result['phone'] = phone;
        }
      }

      // Extract ID numbers (9-12 digits)
      final idRegex = RegExp(r'\b\d{9,12}\b');
      final idMatch = idRegex.firstMatch(line);
      if (idMatch != null && !result.containsKey('idNumber')) {
        result['idNumber'] = idMatch.group(0) ?? '';
      }

      // Extract names - look for patterns after common prefixes
      String cleanLine = line;

      // Remove common prefixes
      final prefixes = [
        'Name:',
        'ឈ្មោះ:',
        'Phone:',
        'ID:',
        'Date:',
        'Address:',
      ];
      for (String prefix in prefixes) {
        if (cleanLine.startsWith(prefix)) {
          cleanLine = cleanLine.substring(prefix.length).trim();
          break;
        }
      }

      // Extract names only if they look like actual names
      if (cleanLine.length > 3 &&
          cleanLine.length < 50 &&
          RegExp(r'^[a-zA-Z\u1780-\u17FF\s]+$').hasMatch(cleanLine)) {
        // Skip common non-name words
        final commonWords = [
          'Random text without useful information',
          'Some numbers',
          'Short text',
          'Invalid phone',
        ];
        bool isCommonWord = commonWords.any((word) => cleanLine.contains(word));

        if (!isCommonWord) {
          if (RegExp(r'[\u1780-\u17FF]').hasMatch(cleanLine) &&
              !result.containsKey('khmerName')) {
            result['khmerName'] = cleanLine;
          } else if (RegExp(r'^[a-zA-Z\s]+$').hasMatch(cleanLine) &&
              !result.containsKey('latinName')) {
            result['latinName'] = cleanLine;
          }
        }
      }

      // Extract dates (various formats)
      final dateRegex = RegExp(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b');
      final dateMatch = dateRegex.firstMatch(line);
      if (dateMatch != null && !result.containsKey('date')) {
        result['date'] = dateMatch.group(0) ?? '';
      }
    }

    return result;
  }
}
