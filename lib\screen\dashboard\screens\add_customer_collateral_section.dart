import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/collateral_type.dart';

class CustomerCollateralSection extends StatelessWidget {
  final Set<CollateralType> selectedCollateralTypes;
  final Map<CollateralType, File?> collateralImages;
  final Function(CollateralType, bool) onCollateralTypeChanged;
  final Function(CollateralType, File?) onImagePicked;

  const CustomerCollateralSection({
    Key? key,
    required this.selectedCollateralTypes,
    required this.collateralImages,
    required this.onCollateralTypeChanged,
    required this.onImagePicked,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('ប្រភេទធានា/Collateral Type', style: TextStyle(fontWeight: FontWeight.bold)),
        ...CollateralType.values.map((type) => CheckboxListTile(
              value: selectedCollateralTypes.contains(type),
              title: Text(type.displayName),
              onChanged: (selected) {
                onCollateralTypeChanged(type, selected ?? false);
              },
            )),
        const SizedBox(height: 16),
        ...selectedCollateralTypes.map((type) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Upload for: ${type.displayName}', style: const TextStyle(fontWeight: FontWeight.w500)),
              // Placeholder for CustomerPhotoCapture widget:
              // Replace with your actual CustomerPhotoCapture implementation
              // Example:
              // CustomerPhotoCapture(
              //   title: type.displayName,
              //   imageFile: imageFile,
              //   onImagePicked: (photo) => onImagePicked(type, photo),
              //   isSelfie: false,
              // ),
              const SizedBox(height: 8),
            ],
          );
        }).toList()
      ],
    );
  }
}
