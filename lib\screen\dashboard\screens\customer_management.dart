import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/customer_controller.dart';
import '../../../models/customer_model.dart';
import '../../../widgets/customer/customer_card.dart';
import 'customer_details_screen.dart';

class CustomerManagement extends StatefulWidget {
  const CustomerManagement({super.key});

  @override
  State<CustomerManagement> createState() => _CustomerManagementState();
}

class _CustomerManagementState extends State<CustomerManagement> {
  final CustomerController customerController = Get.find<CustomerController>();
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatus = 'All';

  // This list will be updated by the filter logic
  List<Customer> _filteredCustomers = [];

  @override
  void initState() {
    super.initState();
    // Initial filter setup
    _filterCustomers();
    // Add listener to search controller to re-filter on text change
    _searchController.addListener(() {
      _filterCustomers();
    });
  }

  void _filterCustomers() {
    final query = _searchController.text.toLowerCase();

    // Start with the full list from the controller
    List<Customer> filtered = customerController.allCustomers.where((customer) {
      final statusMatch = _selectedStatus == 'All' ||
          customer.loanStatus.toString().split('.').last == _selectedStatus;

      if (query.isEmpty) {
        return statusMatch;
      }

      final nameMatch = customer.displayName.toLowerCase().contains(query);
      final phoneMatch = customer.phone.contains(query);
      final nidMatch = customer.nid.contains(query);

      return statusMatch && (nameMatch || phoneMatch || nidMatch);
    }).toList();

    // This needs to be inside the Obx build method to react to changes,
    // but for filtering logic, we can update a local state variable that the ListView.builder uses.
    // To trigger rebuilds correctly with Obx, the logic should be inside it.
    // A simple way is to just assign it here and let Obx handle the rest.
    _filteredCustomers = filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Obx(
        () {
          // Re-run the filter whenever the master list in the controller changes.
          _filterCustomers();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search by phone, name, or NID',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Color(0xFF12306E)),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 0,
                            horizontal: 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    DropdownButton<String>(
                      hint: Text('Status'),
                      value: _selectedStatus,
                      onChanged: (val) {
                        _selectedStatus = val!;
                        _filterCustomers();
                      },
                      items: [
                        DropdownMenuItem<String>(
                          value: 'All',
                          child: Text('All'),
                        ),
                        ...LoanStatus.values.map(
                          (status) => DropdownMenuItem(
                            value: status.toString().split('.').last,
                            child: Text(
                              status
                                  .toString()
                                  .split('.')
                                  .last
                                  .replaceFirst(
                                    status.toString().split('.').last[0],
                                    status
                                        .toString()
                                        .split('.')
                                        .last[0]
                                        .toUpperCase(),
                                  ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              Expanded(
                child: _filteredCustomers.isEmpty
                    ? const Center(
                        child: Text(
                          'No customers found.',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _filteredCustomers.length,
                        itemBuilder: (context, index) {
                          final customer = _filteredCustomers[index];
                          return CustomerCard(
                            customer: customer,
                            onTap: () {
                              Get.to(() => CustomerDetailsScreen(customer: customer));
                            },
                          );
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }
}
