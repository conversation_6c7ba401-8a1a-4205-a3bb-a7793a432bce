import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
import 'package:lc_work_flow/widgets/customer/customer_photo_capture.dart';

class CustomerDocumentsSection extends StatelessWidget {
  final File? idCardImage;
  final Map<CollateralType, File?> collateralImages;
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, File?) onImagePicked;

  const CustomerDocumentsSection({
    Key? key,
    required this.idCardImage,
    required this.collateralImages,
    required this.selectedCollateralTypes,
    required this.onImagePicked,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomerPhotoCapture(
          title: 'ID Card',
          imageFile: idCardImage,
          onImagePicked: (photo) => onImagePicked(CollateralType.hardTitle, photo),
          isSelfie: false,
        ),
        ...selectedCollateralTypes.map((type) {
          File? imageFile = collateralImages[type];
          return CustomerPhotoCapture(
            title: type.displayName,
            imageFile: imageFile,
            onImagePicked: (photo) => onImagePicked(type, photo),
            isSelfie: false,
          );
        }).toList(),
      ],
    );
  }
}
