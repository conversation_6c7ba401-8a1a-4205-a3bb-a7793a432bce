import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import '../../../widgets/customer/customer_details_header.dart';
import '../../../widgets/customer/loan_information_section.dart';
import '../../../widgets/customer/customer_actions.dart';
import '../../../widgets/customer/customer_documents_section.dart';

class CustomerDetailsScreen extends StatelessWidget {
  final Customer customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Implement edit functionality
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer Header
            CustomerDetailsHeader(customer: customer),

            const SizedBox(height: 16),

            // Loan Information
            LoanInformationSection(customer: customer),

            const SizedBox(height: 24),

            // Documents
            CustomerDocumentsSection(customer: customer),

            const SizedBox(height: 24),

            // Action Buttons
            CustomerActions(
              onRecordPayment: () {
                // TODO: Implement payment action
              },
              onEditLoan: () {
                // TODO: Implement edit loan action
              },
              onViewPaymentHistory: () {
                // TODO: Implement view payment history
              },
            ),
          ],
        ),
      ),
    );
  }
}
