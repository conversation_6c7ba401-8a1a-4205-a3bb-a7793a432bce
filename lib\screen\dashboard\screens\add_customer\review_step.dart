import 'package:flutter/material.dart';


class ReviewStep extends StatelessWidget {
  final String fullNameKhmer;
  final String phoneNumber;
  final String fullNameLatin;
  final String idNumber;
  final DateTime? dateOfBirth;
  final String requestedAmount;
  final String productTypeDisplayName;
  final String loanTerm;
  final DateTime? disbursementDate;
  final bool idCardUploaded;
  final bool borrowerNidPhotoUploaded;
  final bool borrowerHomePhotoUploaded;
  final bool borrowerBusinessPhotoUploaded;

  const ReviewStep({
    Key? key,
    required this.fullNameKhmer,
    required this.phoneNumber,
    required this.fullNameLatin,
    required this.idNumber,
    required this.dateOfBirth,
    required this.requestedAmount,
    required this.productTypeDisplayName,
    required this.loanTerm,
    required this.disbursementDate,
    required this.idCardUploaded,
    required this.borrowerNidPhotoUploaded,
    required this.borrowerHomePhotoUploaded,
    required this.borrowerBusinessPhotoUploaded,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(context, 'Personal Information'),
            _buildReviewTile(context, 'Full Name (Khmer)', fullNameKhmer),
            _buildReviewTile(context, 'Full Name (Latin)', fullNameLatin),
            _buildReviewTile(context, 'Phone Number', phoneNumber),
            _buildReviewTile(context, 'ID Number', idNumber),
            _buildReviewTile(context, 'Date of Birth', dateOfBirth != null ? dateOfBirth!.toLocal().toString().split(' ')[0] : '-' ),
            const Divider(),
            _buildSectionTitle(context, 'Loan Information'),
            _buildReviewTile(context, 'Requested Amount', requestedAmount),
            _buildReviewTile(context, 'Product Type', productTypeDisplayName),
            _buildReviewTile(context, 'Loan Term', loanTerm),
            _buildReviewTile(context, 'Disbursement Date', disbursementDate != null ? disbursementDate!.toLocal().toString().split(' ')[0] : '-' ),
            const Divider(),
            _buildSectionTitle(context, 'Documents'),
            _buildDocumentStatusTile('ID Card Uploaded', idCardUploaded, context),
            _buildDocumentStatusTile('NID Photo Uploaded', borrowerNidPhotoUploaded, context),
            _buildDocumentStatusTile('Home Photo Uploaded', borrowerHomePhotoUploaded, context),
            _buildDocumentStatusTile('Business Photo Uploaded', borrowerBusinessPhotoUploaded, context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildReviewTile(BuildContext context, String title, String subtitle) {
    return ListTile(
      dense: true,
      contentPadding: EdgeInsets.zero,
      title: Text(title, style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600)),
    );
  }

  Widget _buildDocumentStatusTile(String title, bool isUploaded, BuildContext context) {
    return ListTile(
      dense: true,
      contentPadding: EdgeInsets.zero,
      title: Text(title, style: Theme.of(context).textTheme.bodyMedium),
      trailing: Icon(
        isUploaded ? Icons.check_circle_outline : Icons.highlight_off,
        color: isUploaded ? Colors.green : Colors.red,
      ),
    );
  }
}
