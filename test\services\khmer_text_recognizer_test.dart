import 'package:flutter_test/flutter_test.dart';
import 'package:lc_work_flow/services/khmer_text_recognizer.dart';

void main() {
  group('KhmerTextRecognizer', () {
    test('extractInformation should extract phone numbers correctly', () {
      const testText = '''
        Name: <PERSON>
        Phone: 012345678
        ID: 123456789012
        Address: Phnom Penh
      ''';
      
      final result = KhmerTextRecognizer.extractInformation(testText);
      
      expect(result['phone'], equals('012345678'));
      expect(result['idNumber'], equals('123456789012'));
      expect(result['latinName'], equals('<PERSON>'));
    });

    test('extractInformation should extract Khmer names correctly', () {
      const testText = '''
        ឈ្មោះ: ចិន សុភក្តី
        Phone: +855123456789
        ID Number: 987654321098
      ''';
      
      final result = KhmerTextRecognizer.extractInformation(testText);
      
      expect(result['phone'], equals('+855123456789'));
      expect(result['idNumber'], equals('987654321098'));
      expect(result['khmerName'], equals('ចិន សុភក្តី'));
    });

    test('extractInformation should extract dates correctly', () {
      const testText = '''
        Name: Test User
        Date of Birth: 15/03/1990
        Phone: 0987654321
      ''';
      
      final result = KhmerTextRecognizer.extractInformation(testText);
      
      expect(result['date'], equals('15/03/1990'));
      expect(result['phone'], equals('0987654321'));
    });

    test('extractInformation should handle mixed content', () {
      const testText = '''
        ឈ្មោះ: ចិន សុភក្តី
        Name: CHEN SOPHEAKDEY
        Phone: +855 12 345 678
        ID: 123456789012
        Date: 01-01-1985
      ''';
      
      final result = KhmerTextRecognizer.extractInformation(testText);
      
      expect(result['khmerName'], equals('ចិន សុភក្តី'));
      expect(result['latinName'], equals('CHEN SOPHEAKDEY'));
      expect(result['phone'], equals('+855 12 345 678'));
      expect(result['idNumber'], equals('123456789012'));
      expect(result['date'], equals('01-01-1985'));
    });

    test('extractInformation should handle empty input', () {
      const testText = '';
      
      final result = KhmerTextRecognizer.extractInformation(testText);
      
      expect(result, isEmpty);
    });

    test('extractInformation should ignore invalid data', () {
      const testText = '''
        Random text without useful information
        Some numbers: 123
        Short text: ab
        Invalid phone: 123
      ''';
      
      final result = KhmerTextRecognizer.extractInformation(testText);
      
      // Should not extract invalid phone numbers or short text
      expect(result['phone'], isNull);
      expect(result['latinName'], isNull);
    });
  });
}
