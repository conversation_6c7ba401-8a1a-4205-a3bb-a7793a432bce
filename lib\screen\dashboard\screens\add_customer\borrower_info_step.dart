import 'package:flutter/material.dart';

import 'package:lc_work_flow/widgets/customer/customer_section_title.dart';
import 'package:lc_work_flow/widgets/customer/customer_text_field.dart';
import 'package:lc_work_flow/widgets/customer/customer_date_picker_field.dart';
import 'package:lc_work_flow/widgets/customer/customer_document_type_dropdown.dart';
import 'package:lc_work_flow/models/document_type.dart';

class BorrowerInfoStep extends StatelessWidget {
  final TextEditingController fullNameKhmerController;
  final TextEditingController fullNameLatinController;
  final TextEditingController phoneController;
  final TextEditingController idNumberController;
  final DocumentType? selectedDocumentType;
  final DateTime? dateOfBirth;
  final Function(DocumentType?) onDocumentTypeChanged;
  final Function(DateTime?) onDateOfBirthChanged;
  final VoidCallback? onScanDocument;

  const BorrowerInfoStep({
    Key? key,
    required this.fullNameKhmerController,
    required this.fullNameLatinController,
    required this.phoneController,
    required this.idNumberController,
    required this.selectedDocumentType,
    required this.dateOfBirth,
    required this.onDocumentTypeChanged,
    required this.onDateOfBirthChanged,
    this.onScanDocument,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Start by selecting a document',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 16),
            CustomerDocumentTypeDropdown(
              selectedDocumentType: selectedDocumentType,
              onChanged: onDocumentTypeChanged,
            ),
            const SizedBox(height: 16),
            if (selectedDocumentType != null)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.document_scanner),
                  label: const Text('Scan Document'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF12306E),
                  ),
                  onPressed: onScanDocument,
                ),
              ),
            const SizedBox(height: 16),
            CustomerSectionTitle(
              title: 'Borrower Information',
              icon: Icons.person_outline,
            ),
            const SizedBox(height: 16),
            CustomerTextField(
              enabled: selectedDocumentType != null,
              controller: phoneController,
              label: 'Phone Number',
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomerTextField(
              enabled: selectedDocumentType != null,
              label: 'Full Name (Khmer)',
              controller: fullNameKhmerController,
              hint: 'Enter full name in Khmer',
              validator:
                  (v) =>
                      v == null || v.isEmpty
                          ? 'Please enter the full name in Khmer'
                          : null,
            ),
            const SizedBox(height: 16),
            CustomerTextField(
              enabled: selectedDocumentType != null,
              label: 'Full Name (Latin)',
              controller: fullNameLatinController,
              hint: 'Enter full name in Latin',
              validator:
                  (v) =>
                      v == null || v.isEmpty
                          ? 'Please enter the full name in Latin'
                          : null,
            ),
            const SizedBox(height: 16),
            CustomerTextField(
              enabled: selectedDocumentType != null,
              label: 'ID Number',
              controller: idNumberController,
              hint: 'Enter ID number',
              validator:
                  (v) =>
                      v == null || v.isEmpty
                          ? 'Please enter the ID number'
                          : null,
            ),
            const SizedBox(height: 16),
            CustomerDatePickerField(
              enabled: selectedDocumentType != null,
              label: 'Date of Birth',
              selectedDate: dateOfBirth,
              onDateSelected: onDateOfBirthChanged,
              validator: (date) {
                if (date == null) {
                  return 'Please select a date of birth';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }
}
