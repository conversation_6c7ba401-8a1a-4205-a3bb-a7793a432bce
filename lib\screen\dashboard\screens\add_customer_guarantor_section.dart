import 'package:flutter/material.dart';

class CustomerGuarantorSection extends StatelessWidget {
  final TextEditingController guarantorNameController;
  final TextEditingController guarantorPhoneController;
  final bool show;

  const CustomerGuarantorSection({
    Key? key,
    required this.guarantorNameController,
    required this.guarantorPhoneController,
    required this.show,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!show) return SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('ព័ត៌មានអ្នកធានា/អ្នករួមខ្ចី', style: TextStyle(fontWeight: FontWeight.bold)),
          TextField(
            controller: guarantorNameController,
            decoration: const InputDecoration(
              labelText: 'Guarantor/Co-borrower Name',
              hintText: 'Enter name',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: guarantorPhoneController,
            decoration: const InputDecoration(
              labelText: 'Guarantor/Co-borrower Phone',
              hintText: 'Enter phone number',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }
}
